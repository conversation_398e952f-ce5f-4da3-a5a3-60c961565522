/**
 * AI Response Parser
 * Parses AI responses to detect and extract structured trade data for card display
 */

import { Trade } from '../types/trade';

export interface DisplayItem {
  type: 'trade' | 'event';
  id: string;
}

export interface ParsedAIResponse {
  textContent: string;
  tradeData?: {
    trades: Trade[];
    title?: string;
    summary?: {
      totalTrades: number;
      totalPnL: number;
      winRate: number;
    };
  };
  displayItems?: DisplayItem[];
  hasStructuredData: boolean;
}

export interface AIResponseWithTrades {
  response: string;
  trades?: Trade[];
  tradesSummary?: {
    title: string;
    totalTrades: number;
    totalPnL: number;
    winRate: number;
  };
}
 

/**
 * Parse AI response to extract trade data and display items
 */
export function parseAIResponse(
  response: string,
  functionCalls?: any[],
  allTrades?: Trade[]
): ParsedAIResponse {
  let textContent = response;
  let tradeData: ParsedAIResponse['tradeData'] = undefined;
  let displayItems: DisplayItem[] = [];
  let hasStructuredData = false;

  // Check for new JSON format in response
  const jsonDisplayItems = extractDisplayItemsFromResponse(response, allTrades);
  if (jsonDisplayItems.length > 0) {
    displayItems = jsonDisplayItems;
    hasStructuredData = true;
    textContent = cleanJsonFromResponse(response);
  }

  // Legacy support: Check for function calls (convertTradeIdsToCards)
  if (functionCalls && shouldDisplayTradeCards(functionCalls)) {
    const result = extractTradeDataFromFunctionCalls(functionCalls, 'convertTradeIdsToCards');
    const trades = result.uniqueTrades;
    if (trades.length > 0) {
      tradeData = {
        trades,
        title: result.title || 'Trade Cards',
        summary: {
          totalTrades: trades.length,
          totalPnL: trades.reduce((sum: number, t: Trade) => sum + t.amount, 0),
          winRate: calculateWinRate(trades)
        }
      };
      hasStructuredData = true;
    }
  }

  return {
    textContent,
    tradeData,
    displayItems,
    hasStructuredData
  };
}



/**
 * Calculate win rate from trades
 */
function calculateWinRate(trades: Trade[]): number {
  if (trades.length === 0) return 0;
  const winTrades = trades.filter(trade => trade.type === 'win');
  return (winTrades.length / trades.length) * 100;
}

 

/**
 * Check if response likely contains trade data that should be displayed as cards
 */
export function shouldDisplayTradeCards(functionCalls?: any[]): boolean {
  // Only check for convertTradeIdsToCards function calls
  if (functionCalls && functionCalls.length > 0) {
    return functionCalls.some(call =>
      call.name === 'convertTradeIdsToCards' &&
      call.result?.success &&
      call.result?.data?.tradeCards &&
      Array.isArray(call.result.data.tradeCards) &&
      call.result.data.tradeCards.length > 0
    );
  }

  return false;
}

/**
 * Check if response contains display items JSON
 */
export function hasDisplayItems(response: string): boolean {
  const jsonPattern = /\[\s*\{[^}]*"type"\s*:\s*"(trade|event)"[^}]*"id"\s*:\s*"[^"]*"[^}]*\}[^\]]*\]/;
  return jsonPattern.test(response);
}



/**
 * Extract display items from AI response JSON
 */
function extractDisplayItemsFromResponse(
  response: string,
  allTrades?: Trade[]
): DisplayItem[] {
  const displayItems: DisplayItem[] = [];

  try {
    // Look for JSON array pattern at the end of the response
    const jsonPattern = /\[\s*\{[^}]*"type"\s*:\s*"(trade|event)"[^}]*"id"\s*:\s*"[^"]*"[^}]*\}[^\]]*\]/g;
    const matches = response.match(jsonPattern);

    if (matches) {
      for (const match of matches) {
        try {
          const items = JSON.parse(match) as DisplayItem[];
          if (Array.isArray(items)) {
            // Validate that the items exist in our data
            for (const item of items) {
              if (item.type === 'trade' && allTrades) {
                const tradeExists = allTrades.some(trade => trade.id === item.id);
                if (tradeExists) {
                  displayItems.push(item);
                }
              } else if (item.type === 'event') {
                // For events, we'll validate existence when displaying
                // Economic events are stored separately and fetched dynamically
                displayItems.push(item);
              }
            }
          }
        } catch (parseError) {
          console.warn('Failed to parse display items JSON:', parseError);
        }
      }
    }
  } catch (error) {
    console.warn('Error extracting display items:', error);
  }

  return displayItems;
}

/**
 * Remove JSON display items from response text
 */
function cleanJsonFromResponse(response: string): string {
  // Remove new JSON display items pattern
  let cleaned = response.replace(/\[\s*\{[^}]*"type"\s*:\s*"(trade|event)"[^}]*"id"\s*:\s*"[^"]*"[^}]*\}[^\]]*\]/g, '');

  // Remove legacy tradeCards pattern
  cleaned = cleaned.replace(/\{"tradeCards":\s*\[[^\]]*\][^}]*\}/gi, '');

  // Clean up extra whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n').trim();

  return cleaned;
}

/**
 * Extract trade data from function calls
 */
export function extractTradeDataFromFunctionCalls(functionCalls: any[], name : string): {title:string,uniqueTrades:Trade[]} {
  const allTrades: Trade[] = [];
  let title = '';

  for (const call of functionCalls) {
    if (call.result?.success && call.result?.data?.trades && call.name === name) {
      title = call.result.data.title || 'Trade Cards';
      const trades = call.result.data.trades.map((trade: any) => { 
        return trade as Trade;
      });
      allTrades.push(...trades);
      break;
    }

    // Also check for best/worst trades in statistics
    if (call.result?.success && call.result?.data) {
      const data = call.result.data;
      if (data.bestTrade) allTrades.push(data.bestTrade);
      if (data.worstTrade && data.worstTrade.id !== data.bestTrade?.id) {
        allTrades.push(data.worstTrade);
      }
    }
  }

  // Remove duplicates based on trade ID
  const uniqueTrades = allTrades.filter((trade, index, self) => 
    index === self.findIndex(t => t.id === trade.id)
  );

  return {
    title,
    uniqueTrades};
}






